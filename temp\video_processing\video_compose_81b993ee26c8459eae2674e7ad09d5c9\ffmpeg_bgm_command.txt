BGM混音命令:
d:\jiedan\7.5 AI配音\ffmpeg\bin\ffmpeg.exe -y -progress d:\jiedan\7.5 AI配音\temp\video_processing\progress_3a92120a82864d5da4330c98cc78d1a1.txt -i d:\jiedan\7.5 AI配音\temp\video_processing\video_compose_81b993ee26c8459eae2674e7ad09d5c9\with_subtitle.mp4 -i D:/jiedan/7.5 AI配音/data\test\d2d87b你结_2.mp3 -i D:/Downloads/ap1014_us1056328906_mii0w1iw8z2ai2iphcu80ooo2ki81120_pi406_mx747499847_s2327572077.mp3 -filter_complex [1:a]volume=1.0[main_audio];[2:a]volume=0.1,aloop=-1:2e+09[bgm_loop];[main_audio][bgm_loop]amix=inputs=2:duration=shortest:normalize=0[mixed_audio] -map 0:v -map [mixed_audio] -c:v h264_nvenc -c:a aac -b:a 128k -profile:a aac_low -ar 44100 -ac 2 -t 34618.369958 -avoid_negative_ts make_zero -fflags +genpts+flush_packets -max_muxing_queue_size 4096 -thread_queue_size 2048 -flush_packets 1 -copyts -start_at_zero -movflags +faststart -g 30 -keyint_min 15 -sc_threshold 40 -preset p1 -tune ull -rc cbr -g 60 -bf 0 -refs 1 d:\jiedan\7.5 AI配音\temp\video_processing\video_compose_81b993ee26c8459eae2674e7ad09d5c9\with_audio.mp4

返回码: 1

视频文件: d:\jiedan\7.5 AI配音\temp\video_processing\video_compose_81b993ee26c8459eae2674e7ad09d5c9\with_subtitle.mp4
音频文件: D:/jiedan/7.5 AI配音/data\test\d2d87b你结_2.mp3
BGM文件: D:/Downloads/ap1014_us1056328906_mii0w1iw8z2ai2iphcu80ooo2ki81120_pi406_mx747499847_s2327572077.mp3
BGM音量: 10% (0.1)
目标时长: 34618.37秒

标准输出:


错误输出:
ffmpeg version 2025-07-01-git-11d1b71c31-essentials_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 15.1.0 (Rev4, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-openal --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      60.  4.101 / 60.  4.101
  libavcodec     62.  4.103 / 62.  4.103
  libavformat    62.  1.101 / 62.  1.101
  libavdevice    62.  0.100 / 62.  0.100
  libavfilter    11.  0.100 / 11.  0.100
  libswscale      9.  0.100 /  9.  0.100
  libswresample   6.  0.100 /  6.  0.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'd:\jiedan\7.5 AI配音\temp\video_processing\video_compose_81b993ee26c8459eae2674e7ad09d5c9\with_subtitle.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf62.1.101
  Duration: 09:36:58.40, start: 0.000000, bitrate: 2140 kb/s
  Stream #0:0[0x1](und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 960x720 [SAR 1:1 DAR 4:3], 2000 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
    Metadata:
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc62.4.103 h264_nvenc
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 131 kb/s (default)
    Metadata:
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
Input #1, mp3, from 'D:/jiedan/7.5 AI配音/data\test\d2d87b你结_2.mp3':
  Metadata:
    encoder         : Lavf62.1.101
  Duration: 09:36:58.37, start: 0.046042, bitrate: 160 kb/s
  Stream #1:0: Audio: mp3 (mp3float), 24000 Hz, mono, fltp, 160 kb/s, start 0.046042
Input #2, mp3, from 'D:/Downloads/ap1014_us1056328906_mii0w1iw8z2ai2iphcu80ooo2ki81120_pi406_mx747499847_s2327572077.mp3':
  Metadata:
    title           : Put A Little Umph In It
    artist          : DJ刘恋
    album           : 空手变花BGM
  Duration: 00:03:34.09, start: 0.025057, bitrate: 128 kb/s
  Stream #2:0: Audio: mp3 (mp3float), 44100 Hz, stereo, fltp, 128 kb/s, start 0.025057
    Metadata:
      encoder         : LAME3.99r
    Side data:
      replaygain: track gain - -7.700000, track peak - unknown, album gain - unknown, album peak - unknown, 
[out#0/mp4 @ 00000244848da580] Codec AVOption sc_threshold (Scene change threshold) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some decoder which was not actually used for any stream.
Stream mapping:
  Stream #1:0 (mp3float) -> volume:default
  Stream #2:0 (mp3float) -> volume:default
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (h264_nvenc))
  amix:default -> Stream #0:1 (aac)
Press [q] to stop, [?] for help
Output #0, mp4, to 'd:\jiedan\7.5 AI配音\temp\video_processing\video_compose_81b993ee26c8459eae2674e7ad09d5c9\with_audio.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf62.1.101
  Stream #0:0(und): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 960x720 [SAR 1:1 DAR 4:3], q=2-31, 2000 kb/s, 30 fps, 15360 tbn (default)
    Metadata:
      encoder         : Lavc62.4.103 h264_nvenc
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
    Side data:
      cpb: bitrate max/min/avg: 0/0/2000000 buffer size: 4000000 vbv_delay: N/A
  Stream #0:1: Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 128 kb/s
    Metadata:
      encoder         : Lavc62.4.103 aac
frame=  362 fps=350 q=28.0 size=    3034KiB time=00:00:00.00 bitrate=N/A speed=   0x elapsed=0:00:01.03    
frame= 1322 fps=854 q=27.0 size=   11370KiB time=00:00:32.08 bitrate=2902.6kbits/s speed=20.7x elapsed=0:00:01.54    
frame= 2247 fps=1090 q=24.0 size=   19418KiB time=00:01:03.18 bitrate=2517.7kbits/s speed=30.6x elapsed=0:00:02.06    
frame= 3223 fps=1251 q=25.0 size=   27870KiB time=00:01:35.61 bitrate=2387.7kbits/s speed=37.1x elapsed=0:00:02.57    
frame= 4270 fps=1375 q=22.0 size=   36970KiB time=00:02:10.44 bitrate=2321.7kbits/s speed=  42x elapsed=0:00:03.10    
frame= 5277 fps=1459 q=15.0 size=   45562KiB time=00:02:43.97 bitrate=2276.2kbits/s speed=45.4x elapsed=0:00:03.61    
frame= 6248 fps=1512 q=25.0 size=   53963KiB time=00:03:16.20 bitrate=2253.0kbits/s speed=47.5x elapsed=0:00:04.13    
frame= 7313 fps=1572 q=27.0 size=   63322KiB time=00:03:51.94 bitrate=2236.5kbits/s speed=49.9x elapsed=0:00:04.65    
frame= 8343 fps=1615 q=28.0 size=   72162KiB time=00:04:26.17 bitrate=2220.9kbits/s speed=51.5x elapsed=0:00:05.16    
frame= 9300 fps=1639 q=22.0 size=   80388KiB time=00:04:58.09 bitrate=2209.1kbits/s speed=52.5x elapsed=0:00:05.67    
frame=10218 fps=1651 q=24.0 size=   88415KiB time=00:05:28.79 bitrate=2202.9kbits/s speed=53.1x elapsed=0:00:06.18    
frame=11155 fps=1664 q=23.0 size=   96486KiB time=00:05:59.95 bitrate=2195.9kbits/s speed=53.7x elapsed=0:00:06.70    
frame=12026 fps=1663 q=27.0 size=  104143KiB time=00:06:28.98 bitrate=2193.3kbits/s speed=53.8x elapsed=0:00:07.22    
frame=12925 fps=1670 q=24.0 size=  111821KiB time=00:06:58.74 bitrate=2187.6kbits/s speed=54.1x elapsed=0:00:07.74    
frame=13886 fps=1683 q=24.0 size=  120163KiB time=00:07:31.07 bitrate=2182.3kbits/s speed=54.7x elapsed=0:00:08.25    
frame=14942 fps=1704 q=24.0 size=  129267KiB time=00:08:06.13 bitrate=2178.3kbits/s speed=55.4x elapsed=0:00:08.76    
frame=15896 fps=1712 q=23.0 size=  137494KiB time=00:08:37.92 bitrate=2174.8kbits/s speed=55.8x elapsed=0:00:09.28    
frame=16910 fps=1726 q=30.0 size=  146472KiB time=00:09:11.61 bitrate=2175.3kbits/s speed=56.3x elapsed=0:00:09.79    
frame=17983 fps=1744 q=26.0 size=  155677KiB time=00:09:47.76 bitrate=2169.7kbits/s speed=  57x elapsed=0:00:10.31    
frame=18968 fps=1750 q=26.0 size=  164120KiB time=00:10:20.41 bitrate=2167.1kbits/s speed=57.2x elapsed=0:00:10.83    
frame=19939 fps=1757 q=24.0 size=  172615KiB time=00:10:52.85 bitrate=2166.0kbits/s speed=57.5x elapsed=0:00:11.35    
frame=20979 fps=1769 q=20.0 size=  181632KiB time=00:11:27.51 bitrate=2164.2kbits/s speed=  58x elapsed=0:00:11.86    
frame=21996 fps=1778 q=21.0 size=  190347KiB time=00:12:01.11 bitrate=2162.4kbits/s speed=58.3x elapsed=0:00:12.37    
frame=23038 fps=1787 q=25.0 size=  199320KiB time=00:12:35.92 bitrate=2160.0kbits/s speed=58.7x elapsed=0:00:12.88    
frame=24085 fps=1795 q=16.0 size=  208510KiB time=00:13:10.96 bitrate=2159.5kbits/s speed=58.9x elapsed=0:00:13.41    
frame=25112 fps=1802 q=21.0 size=  217386KiB time=00:13:45.30 bitrate=2157.8kbits/s speed=59.2x elapsed=0:00:13.93    
frame=26173 fps=1812 q=24.0 size=  226582KiB time=00:14:20.67 bitrate=2156.6kbits/s speed=59.6x elapsed=0:00:14.44    
frame=27235 fps=1821 q=21.0 size=  235611KiB time=00:14:55.70 bitrate=2154.9kbits/s speed=59.9x elapsed=0:00:14.95    
frame=28261 fps=1826 q=23.0 size=  244511KiB time=00:15:29.86 bitrate=2154.1kbits/s speed=60.1x elapsed=0:00:15.47    
frame=29198 fps=1826 q=23.0 size=  252737KiB time=00:16:01.23 bitrate=2153.9kbits/s speed=60.1x elapsed=0:00:15.99    
frame=30151 fps=1827 q=21.0 size=  261030KiB time=00:16:33.30 bitrate=2152.8kbits/s speed=60.2x elapsed=0:00:16.50    
frame=31114 fps=1828 q=23.0 size=  269337KiB time=00:17:05.41 bitrate=2151.7kbits/s speed=60.3x elapsed=0:00:17.01    
frame=32223 fps=1838 q=24.0 size=  278924KiB time=00:17:42.33 bitrate=2150.9kbits/s speed=60.6x elapsed=0:00:17.53    
frame=33286 fps=1845 q=24.0 size=  288087KiB time=00:18:17.79 bitrate=2149.8kbits/s speed=60.8x elapsed=0:00:18.04    
frame=34349 fps=1849 q=22.0 size=  297306KiB time=00:18:52.85 bitrate=2149.9kbits/s speed=  61x elapsed=0:00:18.57    
frame=35404 fps=1855 q=27.0 size=  306501KiB time=00:19:28.35 bitrate=2149.0kbits/s speed=61.2x elapsed=0:00:19.09    
frame=36498 fps=1862 q=26.0 size=  315977KiB time=00:20:04.72 bitrate=2148.6kbits/s speed=61.5x elapsed=0:00:19.60    

