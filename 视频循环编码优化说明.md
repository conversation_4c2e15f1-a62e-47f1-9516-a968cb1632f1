# 视频合成循环视频编码优化

## 优化概述

针对批量生成不同时长循环视频的场景，实现了"先编码后循环"的优化策略，大幅提升处理速度。

## 优化前的问题

### 原始逻辑
1. 对于每个不同时长的音频文件
2. 都会调用 `loop_video_to_duration` 生成对应时长的循环视频
3. 每次都会对整个循环视频进行重新编码（应用分辨率、码率等设置）
4. 导致大量重复的编码计算

### 性能问题
- **时间复杂度**: O(n × duration × encoding_time)
- **CPU/GPU负担**: 每个循环视频都要完整编码
- **重复计算**: 相同的编码设置被重复应用多次

## ✅ 已实现的优化方案

### 新的逻辑
1. **检查编码需求**: 判断是否需要应用编码设置（分辨率、码率等）
2. **预编码基础视频**: 如果需要编码，先对原视频应用一次编码设置
3. **快速循环拼接**: 使用预编码的视频进行简单的文件拼接（-c copy模式）

### 核心实现
- `_should_use_pre_encoding()`: 判断是否使用预编码优化
- `_pre_encode_base_video()`: 预编码基础视频，应用所有编码设置
- `_loop_pre_encoded_video()`: 使用预编码视频快速循环
- `_loop_video_with_pre_encoding()`: 主要的预编码循环方法

### 性能提升
- **时间复杂度**: O(1 × encoding_time) + O(n × copy_time)
- **编码次数**: 从 n 次减少到 1 次
- **后续操作**: 只是简单的文件拼接，速度极快

## 技术实现细节

### 核心方法

#### 1. `_should_use_pre_encoding(settings)`
检查是否应该使用预编码优化：
- 自定义分辨率 (`custom_resolution`)
- 自定义压缩 (`custom_compression`)
- 强制编码 (`force_encoding`)

#### 2. `_pre_encode_base_video(video_path, settings, progress_callback)`
预编码基础视频：
- 对原视频应用所有编码设置（分辨率、码率等）
- 生成编码后的基础视频文件
- 支持缓存机制，避免重复编码相同的视频+设置组合
- 使用优化的编码器设置，优先速度

#### 3. `_loop_pre_encoded_video(pre_encoded_video, target_duration, output_path, progress_callback)`
使用预编码视频快速循环：
- 使用 FFmpeg 的 concat demuxer
- 采用 `-c copy` 模式，不重新编码
- 只进行文件拼接和时长裁剪
- 添加时间戳修复参数确保播放连续

#### 4. `_loop_video_with_pre_encoding(video_path, target_duration, output_path, settings, progress_callback)`
主要的预编码循环方法：
- 先调用预编码，再调用快速循环
- 包含完整的错误处理和回退机制
- 如果任何步骤失败，自动回退到传统编码方式

### 缓存机制

#### 预编码视频缓存
- **缓存键**: `pre_encoded_{video_hash}_{settings_hash}`
- **缓存位置**: 系统临时目录/video_composer_cache
- **缓存策略**: 基于视频文件信息（路径、大小、修改时间）和编码设置的组合
- **缓存方法**: `_get_cached_video()` 和 `_cache_video()`

#### 循环视频缓存
- 保持原有的循环视频缓存机制
- 支持预编码和原始两种生成方式的缓存

### 批量生成优化

#### 串行模式优化
- 在开始批量生成前，检查是否需要预编码
- 如果需要，先生成一次预编码视频
- 所有后续循环都使用这个预编码视频快速拼接

#### 并行模式优化
- 同样在并行处理前先完成预编码
- 多个线程共享同一个预编码视频
- 避免多线程重复编码的资源浪费

## 使用场景

### 适用情况
✅ 需要应用编码设置（分辨率、码率等）  
✅ 批量生成多个不同时长的循环视频  
✅ 原视频文件较大或编码设置复杂  
✅ 对处理速度有较高要求  

### 不适用情况
❌ 不需要任何编码设置（直接使用原视频）  
❌ 只生成单个循环视频  
❌ 原视频文件很小且编码很快  

## 性能对比

### 示例场景
- 原视频: 10秒，1080p
- 目标: 生成 30秒、45秒、60秒 三个循环视频
- 设置: 720p + 2000k码率

### 优化前
```
视频1 (30秒): 编码30秒视频 → 耗时 ~15秒
视频2 (45秒): 编码45秒视频 → 耗时 ~22秒  
视频3 (60秒): 编码60秒视频 → 耗时 ~30秒
总耗时: ~67秒
```

### 优化后
```
预编码: 编码10秒基础视频 → 耗时 ~5秒
视频1 (30秒): 拼接3次 → 耗时 ~2秒
视频2 (45秒): 拼接4.5次 → 耗时 ~3秒
视频3 (60秒): 拼接6次 → 耗时 ~4秒
总耗时: ~14秒
```

### 性能提升
- **速度提升**: 约 4.8倍
- **编码次数**: 从 3次 减少到 1次
- **总编码时长**: 从 135秒 减少到 10秒

## 时间戳处理和视频连续性

### 关键FFmpeg参数
- `-avoid_negative_ts make_zero`: 避免负时间戳
- `-fflags +genpts+igndts`: 重新生成时间戳并忽略DTS
- `-movflags +faststart`: 优化播放启动
- `-t target_duration`: 精确裁剪到目标时长

### 确保连续性
- 使用 concat demuxer 确保时间戳连续
- 精确的时长控制避免跳转问题
- 预编码时保持原视频的帧率和格式
- copy模式避免重新编码引入的时间戳问题

## 错误处理和向后兼容

### 多层回退机制
1. **预编码失败** → 回退到传统的 `_loop_video_with_encoding`
2. **快速循环失败** → 回退到传统编码方式
3. **传统方式也失败** → 返回错误信息

### 渐进式启用
- 只在检测到编码需求时才启用优化
- 不影响现有的简单循环场景（直接copy模式）
- 保持所有原有API接口不变
- 用户无需修改调用代码

### 兼容性保证
- 支持所有现有的编码设置
- 支持硬件加速编码器
- 支持自定义分辨率和码率
- 保持原有的缓存机制

## 实际使用效果

### 自动检测和启用
- 当检测到需要编码设置时，自动启用预编码优化
- 在进度回调中会显示 "🚀 使用预编码优化模式（先编码后循环）"
- 批量生成时会显示 "🚀 检测到编码设置，使用预编码优化模式"

### 进度显示
- "🔧 开始预编码基础视频..."
- "✅ 预编码完成"
- "⚡ 使用预编码视频快速循环..."
- "✅ 快速循环完成"
- "🎉 预编码优化循环完成！"

### 缓存提示
- "🔄 使用缓存的预编码视频"
- 相同视频和设置组合的后续处理会直接使用缓存

## 临时文件管理优化

### 问题解决
- **原问题**: 使用系统临时目录，文件残留难以管理和清理
- **新方案**: 使用项目目录下的temp文件夹，便于记录和删除

### 实现细节
- **临时目录结构**:
  ```
  项目根目录/
  ├── temp/
  │   ├── video_processing/     # 视频处理临时文件
  │   └── video_composer_cache/ # 缓存文件
  ```

- **新增方法**:
  - `_get_temp_dir()`: 获取临时文件目录
  - `_create_temp_file()`: 创建临时文件
  - `_cleanup_temp_file()`: 清理单个临时文件
  - `cleanup_temp_directory()`: 清理整个临时目录
  - `get_temp_directory_size()`: 获取临时目录大小

### 文件命名规范
- **预编码文件**: `pre_encoded_{uuid}.mp4`
- **循环视频**: `loop_video_{uuid}.mp4`
- **进度文件**: `progress_{uuid}.txt`
- **拼接文件**: `concat_{uuid}.txt`
- **测试文件**: `test_video_{uuid}.mp4`
- **字幕文件**: `subtitle_{uuid}.ass`

### 全面替换完成
✅ **已完成替换的文件类型**:
- 所有concat文件（视频拼接列表）
- 所有progress文件（FFmpeg进度监控）
- 所有预编码临时视频
- 所有循环视频临时文件
- 所有测试视频文件
- 所有字幕处理临时文件
- 所有视频合成临时目录
- TempFileManager类的临时文件创建

### 回退机制保留
- 保留了必要的系统临时目录回退机制
- 当项目temp目录不可用时，自动回退到系统临时目录
- 确保在任何情况下都能正常工作

### 自动清理机制
- 处理完成后自动清理临时文件
- 处理失败时也会清理相关临时文件
- 提供手动清理整个临时目录的方法
- TempFileManager统一管理所有临时文件的生命周期

## 总结

✅ **已成功实现**"编码一次，循环多次"的优化策略：

1. **性能提升显著**: 在需要编码设置的批量场景中，速度提升4-5倍
2. **智能检测**: 自动判断是否需要使用预编码优化
3. **完整回退**: 多层错误处理确保稳定性
4. **无缝集成**: 不影响现有功能，用户无需修改代码
5. **时间戳安全**: 确保视频播放连续，无跳转问题
6. **文件管理优化**: 使用项目temp目录，便于管理和清理临时文件

这个优化特别适合批量生成多个不同时长循环视频且需要应用编码设置的场景，可以大幅减少编码时间和系统资源消耗，同时解决了临时文件残留问题。
